// RtcSessionManager.swift
import Foundation

/// 会话管理器 - 管理所有WebRTC会话和信令连接
actor RtcSessionManager {
    static let shared = RtcSessionManager()
    
    // MARK: - 会话管理
    
    /// 管理所有WebRTC会话
    private var sessions: [String: WebRTCSession] = [:]
    
    /// 会话空闲计时器
    private var sessionEmptyTimer: Timer?
    private let sessionEmptyTimeout: TimeInterval = 5 * 60 // 5分钟
    
    // MARK: - 共享组件
    
    private let sharedSignalingClient: SignalingClient
    private let sharedScreenCapture: ScreenCapture
    
    // MARK: - 连接管理
    
    /// 连接重试计数
    private var connectRetryCount = 0
    private let maxConnectRetries = 5
    
    // MARK: - 日志
    
    private let logger = DaLog(subsystem: "com.iantsrtc.manager", category: "RtcSessionManager")
    
    // MARK: - 初始化
    
    private init() {
        self.sharedSignalingClient = SignalingClient.shared
        self.sharedScreenCapture = ScreenCapture.shared
        
        // 设置信令客户端委托
        sharedSignalingClient.delegate = self

        Task {
            await logger.info("🎯 RtcSessionManager 初始化完成")
        }
    }
    
    // MARK: - 对外接口
    
    /// 启动 RTC 会话管理器
    /// - Returns: 启动结果
    func start() async -> (success: Bool, error: String?) {
        logger.info("🚀 启动 RtcSessionManager")

        // 检查信令客户端状态
        let isConnected = await sharedSignalingClient.isConnected

        if !isConnected {
            // 尝试连接
            let connectResult = await attemptConnect()
            if !connectResult.success {
                return (false, connectResult.error)
            }
        }

        logger.info("✅ RtcSessionManager 启动成功")
        return (true, nil)
    }
    
    /// 尝试连接信令服务器
    /// - Returns: 连接结果
    private func attemptConnect() async -> (success: Bool, error: String?) {
        connectRetryCount = 0
        
        while connectRetryCount < maxConnectRetries {
            do {
                try await sharedSignalingClient.connect()
                logger.info("✅ 信令服务器连接成功")
                connectRetryCount = 0
                return (true, nil)
            } catch {
                connectRetryCount += 1
                logger.warning("⚠️ 连接失败，重试 \(connectRetryCount)/\(maxConnectRetries): \(error)")
                
                if connectRetryCount < maxConnectRetries {
                    // 等待一段时间后重试
                    try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
                }
            }
        }
        
        let errorMessage = "连接失败，已重试 \(maxConnectRetries) 次"
        logger.error("❌ \(errorMessage)")
        return (false, errorMessage)
    }
    
    // MARK: - 会话管理
    
    /// 动态创建WebRTC会话
    /// - Parameters:
    ///   - targetID: 目标ID
    ///   - rawSourceID: 原始 sourceID (包含控制者编号)
    ///   - rtcType: 会话类型
    /// - Returns: 创建的会话
    private func createSession(for sourceID: String, rtcType: RtcType) async throws -> WebRTCSession {
        // 解析控制者编号
        let (controllerNumber, _) = parseSourceID(sourceID)

        // 生成客户端ID，使用控制者编号作为前缀
        let baseId = UUID().uuidString.replacingOccurrences(of: "-", with: "").prefix(8).lowercased()
        let clientId = if let controllerNumber = controllerNumber {
            "\(controllerNumber)_\(baseId)"
        } else {
            String(baseId)
        }

        logger.info("🆔 生成客户端ID: \(clientId) (控制者编号: \(controllerNumber ?? "无"))")
        
        // 创建会话
        let session = WebRTCSession(
            clientId: clientId,
            rtcType: rtcType,
            targetID: sourceID,
            signalingClient: sharedSignalingClient,
            screenCapture: sharedScreenCapture
        )
        
        // 设置委托
        session.delegate = self
        
        // 添加到会话管理
        sessions[clientId] = session
        
        // 启动会话
        try session.start()
        
        logger.info("✅ 创建会话成功: \(clientId), 类型: \(rtcType.rawValue), 目标: \(sourceID)")
        
        // 取消空闲计时器
        cancelSessionEmptyTimer()
        
        return session
    }
    
    /// 移除会话
    /// - Parameter clientId: 客户端ID
    private func removeSession(for clientId: String) async {
        guard let session = sessions.removeValue(forKey: clientId) else { return }
        
        session.stop()
        logger.info("✅ 会话 \(clientId) 已移除，当前会话数: \(sessions.count)")
        
        // 如果会话为空，启动空闲计时器
        if sessions.isEmpty {
            startSessionEmptyTimer()
        }
    }
    
    /// 启动会话空闲计时器
    private func startSessionEmptyTimer() {
        cancelSessionEmptyTimer()
        
        sessionEmptyTimer = Timer.scheduledTimer(withTimeInterval: sessionEmptyTimeout, repeats: false) { [weak self] _ in
            Task {
                await self?.handleSessionEmptyTimeout()
            }
        }
        
        logger.info("⏰ 启动会话空闲计时器，5分钟后自动断开连接")
    }
    
    /// 取消会话空闲计时器
    private func cancelSessionEmptyTimer() {
        sessionEmptyTimer?.invalidate()
        sessionEmptyTimer = nil
    }
    
    /// 处理会话空闲超时
    private func handleSessionEmptyTimeout() async {
        logger.info("⏰ 会话空闲超时，自动断开信令连接")
        await sharedSignalingClient.disconnect()
    }
    
    // MARK: - 状态查询
    
    /// 获取会话数量
    func getSessionCount() -> Int {
        return sessions.count
    }
    
    /// 获取所有活跃会话
    func getActiveSessions() -> [WebRTCSession] {
        return Array(sessions.values)
    }
    
    /// 获取管理器状态
    func getManagerStatus() async -> [String: Any] {
        let signalingStatus = await sharedSignalingClient.getConnectionStatus()
        let screenStatus = ["delegateCount": sharedScreenCapture.getDelegateCount()]
        
        return [
            "sessionCount": sessions.count,
            "signalingClient": signalingStatus,
            "screenCapture": screenStatus,
            "connectRetryCount": connectRetryCount
        ]
    }
    
    deinit {
        logger.info("♻️ RtcSessionManager 析构")
        sessionEmptyTimer?.invalidate()
    }
}

// MARK: - SignalingClientDelegate

extension RtcSessionManager: SignalingClientDelegate {
    nonisolated func signalingClient(_ client: SignalingClient, didReceiveMessage message: SignalingMessage) {
        Task {
            await logger.debug("📥 收到信令消息，原始消息内容: \(message)")
            await handleSignalingMessage(message)
        }
    }

    nonisolated func signalingClientDidConnect(_ client: SignalingClient) {
        Task {
            await logger.info("📡 信令客户端已连接")
        }
    }

    nonisolated func signalingClientDidDisconnect(_ client: SignalingClient) {
        Task {
            await logger.warning("📡 信令客户端已断开连接")
        }
    }
    
    /// 处理信令消息
    private func handleSignalingMessage(_ message: SignalingMessage) async {
        logger.debug("📥 处理信令消息: \(message.type),消息内容: \(message)")
        
        switch message.type {
        case SignalingMessageType.greeting:
            await handleGreetingMessage(message)
            
        case SignalingMessageType.answer:
            await handleAnswerMessage(message)
            
        case SignalingMessageType.iceCandidate:
            await handleIceCandidateMessage(message)
            
        case SignalingMessageType.updateScreenSettings:
            await handleUpdateScreenSettingsMessage(message)
            
        default:
            logger.debug("📝 其他消息类型: \(message.type)")
        }
    }
    
    /// 处理问候消息
    private func handleGreetingMessage(_ message: SignalingMessage) async {
        // 从 sourceID 中解析信息
        guard let sourceID = message.sourceID else {
            logger.warning("⚠️ Greeting 消息缺少 sourceID")
            return
        }

        // 解析 sourceID，格式：main_435524_KYLQKL9WQL
        let (controllerNumber, actualSourceID) = parseSourceID(sourceID)

        logger.info("👋 处理 Greeting 消息")
        logger.info("   原始 sourceID: \(sourceID)")
        logger.info("   控制者编号: \(controllerNumber ?? "未知")")
        logger.info("   实际 sourceID: \(actualSourceID)")

        do {
            // 创建新的WebRTC会话，默认使用 iAnts 模式
            if case .string(let dataString) = message.data, dataString == "哈喽!" {
                // 使用 dataAnt 模式
                let session = try await createSession(for: sourceID, rtcType: .dataAnt)
                logger.info("✅ 响应 Greeting 消息，dataAnt模式,创建会话: \(session.clientId)")
            } else {
                let session = try await createSession(for: sourceID, rtcType: .iAnts)
                logger.info("✅ 响应 Greeting 消息，iAnts模式,创建会话: \(session.clientId)")
            }

        } catch {
            logger.error("❌ 处理 Greeting 消息失败: \(error)")
        }
    }

    /// 解析 sourceID，提取控制者编号和实际 sourceID
    /// - Parameter rawSourceID: 原始 sourceID，格式：main_435524_KYLQKL9WQL
    /// - Returns: (控制者编号, 实际sourceID)
    private func parseSourceID(_ rawSourceID: String) -> (String?, String) {
        let components = rawSourceID.components(separatedBy: "_")

        if components.count >= 3 && components[0] == "main" {
            let controllerNumber = components[1]
            let actualSourceID = components[2]
            return (controllerNumber, actualSourceID)
        } else {
            // 如果格式不匹配，返回原始值
            return (nil, rawSourceID)
        }
    }
    
    /// 处理答复消息
    private func handleAnswerMessage(_ message: SignalingMessage) async {
        guard let targetID = message.targetID,
              let data = message.data,
              case .webrtcAnswer(let answerData) = data else {
            logger.warning("⚠️ Answer 消息格式错误")
            return
        }
        
        // 找到对应的会话
        let session = sessions.values.first { $0.targetID == targetID }
        
        if let session = session {
            do {
                try session.handleAnswer(answerData.sdp)
                logger.info("✅ 处理 Answer 消息成功")
            } catch {
                logger.error("❌ 处理 Answer 消息失败: \(error)")
            }
        } else {
            logger.warning("⚠️ 找不到对应的会话: \(targetID)")
        }
    }
    
    /// 处理ICE候选消息
    private func handleIceCandidateMessage(_ message: SignalingMessage) async {
        guard let targetID = message.targetID,
              let data = message.data,
              case .iceCandidate(let candidateData) = data else {
            logger.warning("⚠️ ICE候选消息格式错误")
            return
        }
        
        // 找到对应的会话
        let session = sessions.values.first { $0.targetID == targetID }
        
        if let session = session {
            session.handleIceCandidate(candidateData)
            logger.debug("✅ 处理 ICE候选消息成功")
        } else {
            logger.warning("⚠️ 找不到对应的会话: \(targetID)")
        }
    }
    
    /// 处理屏幕设置更新消息
    private func handleUpdateScreenSettingsMessage(_ message: SignalingMessage) async {
        logger.info("⚙️ 处理屏幕设置更新消息")
        
        // 这里可以根据消息内容更新屏幕设置
        // 例如：分辨率、帧率、编码参数等
        
        if let data = message.data,
           case .screenSettings(let settings) = data {
            logger.info("📱 屏幕设置: \(settings)")
            
            // 应用设置到 ScreenCapture 或其他组件
            // await sharedScreenCapture.updateSettings(settings)
        }
    }
}

// MARK: - WebRTCSessionDelegate

extension RtcSessionManager: WebRTCSessionDelegate {
    nonisolated func webRTCSessionDidDisconnect(_ session: WebRTCSession) {
        Task {
            await removeSession(for: session.clientId)
        }
    }
}
