import Foundation
import CoreGraphics

/// 输入事件类型 (对应 Java 代码中的事件)
enum InputEventType {
    case input   // INPUT - 键盘输入
    case scroll  // SCROLL - 滚动
    case down    // DOWN - 触摸按下
    case up      // UP - 触摸抬起
    case move    // MOVE - 触摸移动
    // case shell - 暂时忽略
}

/// 输入事件基类
protocol InputEvent {
    var type: InputEventType { get }
}

/// 键盘输入事件 (对应 INPUT)
struct KeyboardInputEvent: InputEvent {
    let type: InputEventType = .input
    let text: String

    init(text: String) {
        self.text = text
    }
}

/// 滚动事件 (对应 SCROLL)
struct ScrollEvent: InputEvent {
    let type: InputEventType = .scroll
    let deltaH: CGFloat  // 对应 Java 中的 h 参数

    init(deltaH: CGFloat) {
        self.deltaH = deltaH
    }
}

/// 触摸事件 (对应 DOWN/UP/MOVE)
struct TouchEvent: InputEvent {
    let type: InputEventType
    let positionX: CGFloat  // 对应 Java 中的 x 参数
    let positionY: CGFloat  // 对应 Java 中的 y 参数

    init(type: InputEventType, positionX: CGFloat, positionY: CGFloat) {
        self.type = type
        self.positionX = positionX
        self.positionY = positionY
    }
}

/// 输入事件生成器，从WebRTC数据通道收到的消息创建输入事件
class InputEventFactory {
    /// 根据JSON数据创建输入事件
    /// - Parameter data: JSON数据
    /// - Returns: 输入事件
    static func createEvent(from data: Data) -> InputEvent? {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }

        guard let typeString = json["type"] as? String else {
            return nil
        }

        switch typeString {
        case "INPUT":
            return createKeyboardInputEvent(from: json)
        case "SCROLL":
            return createScrollEvent(from: json)
        case "DOWN":
            return createTouchDownEvent(from: json)
        case "UP":
            return createTouchUpEvent(from: json)
        case "MOVE":
            return createTouchMoveEvent(from: json)
        default:
            return nil
        }
    }

    /// 创建键盘输入事件 (对应 INPUT)
    private static func createKeyboardInputEvent(from json: [String: Any]) -> KeyboardInputEvent? {
        guard let word = json["word"] as? String else {
            return nil
        }

        return KeyboardInputEvent(text: word)
    }

    /// 创建滚动事件 (对应 SCROLL)
    private static func createScrollEvent(from json: [String: Any]) -> ScrollEvent? {
        guard let deltaH = json["h"] as? CGFloat else {
            return nil
        }

        return ScrollEvent(deltaH: deltaH)
    }

    /// 创建触摸按下事件 (对应 DOWN)
    private static func createTouchDownEvent(from json: [String: Any]) -> TouchEvent? {
        guard let posX = json["x"] as? CGFloat,
              let posY = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .down, positionX: posX, positionY: posY)
    }

    /// 创建触摸抬起事件 (对应 UP)
    private static func createTouchUpEvent(from json: [String: Any]) -> TouchEvent? {
        guard let posX = json["x"] as? CGFloat,
              let posY = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .up, positionX: posX, positionY: posY)
    }

    /// 创建触摸移动事件 (对应 MOVE)
    private static func createTouchMoveEvent(from json: [String: Any]) -> TouchEvent? {
        guard let posX = json["x"] as? CGFloat,
              let posY = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .move, positionX: posX, positionY: posY)
    }
} 