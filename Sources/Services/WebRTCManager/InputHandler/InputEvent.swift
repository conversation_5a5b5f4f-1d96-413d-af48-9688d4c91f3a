import Foundation
import CoreGraphics
import UIKit

/// 输入事件类型 (对应 Java 代码中的事件)
enum InputEventType {
    case input   // INPUT - 键盘输入
    case scroll  // SCROLL - 滚动
    case down    // DOWN - 触摸按下
    case up      // UP - 触摸抬起
    case move    // MOVE - 触摸移动
    // case shell - 暂时忽略
}

/// 输入事件基类
protocol InputEvent {
    var type: InputEventType { get }
}

/// 键盘输入事件 (对应 INPUT)
struct KeyboardInputEvent: InputEvent {
    let type: InputEventType = .input
    let text: String

    init(text: String) {
        self.text = text
    }
}

/// 滚动事件 (对应 SCROLL)
struct ScrollEvent: InputEvent {
    let type: InputEventType = .scroll
    let deltaH: CGFloat  // 对应 Java 中的 h 参数

    init(deltaH: CGFloat) {
        self.deltaH = deltaH
    }
}

/// 触摸事件 (对应 DOWN/UP/MOVE)
struct TouchEvent: InputEvent {
    let type: InputEventType
    let positionX: CGFloat  // 对应 Java 中的 x 参数
    let positionY: CGFloat  // 对应 Java 中的 y 参数

    init(type: InputEventType, positionX: CGFloat, positionY: CGFloat) {
        self.type = type
        self.positionX = positionX
        self.positionY = positionY
    }
}

/// 输入事件生成器，从WebRTC数据通道收到的消息创建输入事件
class InputEventFactory {
    /// 根据JSON数据创建输入事件
    /// - Parameter data: JSON数据
    /// - Returns: 输入事件
    static func createEvent(from data: Data) -> InputEvent? {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }

        guard let typeString = json["type"] as? String else {
            return nil
        }

        switch typeString {
        case "INPUT":
            return createKeyboardInputEvent(from: json)
        case "SCROLL":
            return createScrollEvent(from: json)
        case "DOWN":
            return createTouchDownEvent(from: json)
        case "UP":
            return createTouchUpEvent(from: json)
        case "MOVE":
            return createTouchMoveEvent(from: json)
        default:
            return nil
        }
    }

    /// 创建键盘输入事件 (对应 INPUT)
    private static func createKeyboardInputEvent(from json: [String: Any]) -> KeyboardInputEvent? {
        guard let word = json["word"] as? String else {
            return nil
        }

        return KeyboardInputEvent(text: word)
    }

    /// 创建滚动事件 (对应 SCROLL)
    private static func createScrollEvent(from json: [String: Any]) -> ScrollEvent? {
        guard let deltaH = json["h"] as? CGFloat else {
            return nil
        }

        return ScrollEvent(deltaH: deltaH)
    }

    /// 创建触摸按下事件 (对应 DOWN)
    private static func createTouchDownEvent(from json: [String: Any]) -> TouchEvent? {
        guard let posX = json["x"] as? CGFloat,
              let posY = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .down, positionX: posX, positionY: posY)
    }

    /// 创建触摸抬起事件 (对应 UP)
    private static func createTouchUpEvent(from json: [String: Any]) -> TouchEvent? {
        guard let posX = json["x"] as? CGFloat,
              let posY = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .up, positionX: posX, positionY: posY)
    }

    /// 创建触摸移动事件 (对应 MOVE)
    private static func createTouchMoveEvent(from json: [String: Any]) -> TouchEvent? {
        guard let posX = json["x"] as? CGFloat,
              let posY = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .move, positionX: posX, positionY: posY)
    }
}

// MARK: - 输入事件处理器单例

/// 输入事件坐标转换器（单例）
class InputEventCoordinator {
    static let shared = InputEventCoordinator()

    private let logger = DaLog(subsystem: "com.iantsrtc.input", category: "InputEventCoordinator")

    // 屏幕尺寸信息（与视频编码器保持一致）
    private let actualScreenSize: CGSize
    private let videoScreenSize: CGSize

    private init() {
        // 获取实际屏幕尺寸
        let screenBounds = UIScreen.main.bounds
        let screenScale = UIScreen.main.scale
        let actualWidth = screenBounds.width * screenScale
        let actualHeight = screenBounds.height * screenScale
        self.actualScreenSize = CGSize(width: actualWidth, height: actualHeight)

        // 计算视频屏幕尺寸（与VideoCapturer相同的逻辑：高度960，等比例宽度）
        let targetHeight: CGFloat = 960
        let aspectRatio = actualWidth / actualHeight
        let targetWidth = targetHeight * aspectRatio
        self.videoScreenSize = CGSize(width: targetWidth, height: targetHeight)

        logger.info("🖥️ 屏幕尺寸初始化完成")
        logger.info("   实际屏幕: \(Int(actualScreenSize.width))x\(Int(actualScreenSize.height))")
        logger.info("   视频屏幕: \(Int(videoScreenSize.width))x\(Int(videoScreenSize.height))")
    }

    /// 将相对坐标转换为实际屏幕像素坐标
    func convertToActualScreenCoordinates(relativeX: CGFloat, relativeY: CGFloat) -> CGPoint {
        let actualX = relativeX * actualScreenSize.width
        let actualY = relativeY * actualScreenSize.height
        return CGPoint(x: actualX, y: actualY)
    }

    /// 获取实际屏幕尺寸
    var getActualScreenSize: CGSize {
        return actualScreenSize
    }
}

/// 输入事件处理器（单例）
class InputEventProcessor {
    static let shared = InputEventProcessor()

    private let logger = DaLog(subsystem: "com.iantsrtc.input", category: "InputEventProcessor")
    private let coordinator = InputEventCoordinator.shared
    private let inputHandler = InputEventHandler()

    private init() {
        // 设置输入处理器的屏幕尺寸
        inputHandler.setScreenSize(coordinator.getActualScreenSize)
        logger.info("🎮 输入事件处理器初始化完成")
    }

    /// 处理输入事件（主要入口方法）
    /// - Parameter jsonData: JSON数据
    func handleInputEvent(jsonData: Data) {
        logger.debug("🎮 处理输入事件，数据大小: \(jsonData.count) 字节")

        // 使用 InputEventFactory 解析事件
        guard let inputEvent = InputEventFactory.createEvent(from: jsonData) else {
            logger.warning("⚠️ 无法解析输入事件: \(String(data: jsonData, encoding: .utf8) ?? "无效数据")")
            return
        }

        // 记录详细的事件信息并转换坐标
        logEventDetails(inputEvent)

        // 处理输入事件
        inputHandler.handleInputEvent(inputEvent)
    }

    /// 记录事件详细信息
    private func logEventDetails(_ inputEvent: InputEvent) {
        switch inputEvent.type {
        case .input:
            if let keyboardEvent = inputEvent as? KeyboardInputEvent {
                logger.debug("📝 键盘输入: \(keyboardEvent.text)")
            }
        case .scroll:
            if let scrollEvent = inputEvent as? ScrollEvent {
                logger.debug("📜 滚动: \(scrollEvent.deltaH)")
            }
        case .down, .up, .move:
            if let touchEvent = inputEvent as? TouchEvent {
                let actualCoord = coordinator.convertToActualScreenCoordinates(
                    relativeX: touchEvent.positionX,
                    relativeY: touchEvent.positionY
                )
                logger.debug("👆 触摸\(touchEvent.type): (\(Int(actualCoord.x)), \(Int(actualCoord.y)))")
            }
        }
    }
}