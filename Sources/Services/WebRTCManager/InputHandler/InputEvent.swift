import Foundation
import CoreGraphics

/// 输入事件类型
enum InputEventType {
    case mouseMove
    case mouseDown
    case mouseUp
    case keyDown
    case keyUp
    case scroll
    case input
    case down  // 从日志中看到的 DOWN 事件
    case up    // 从日志中看到的 UP 事件
}

/// 鼠标按键
enum MouseButton: Int {
    case left = 1
    case middle = 2
    case right = 3
    case none = 0
}

/// 输入事件基类
protocol InputEvent {
    var type: InputEventType { get }
}

/// 鼠标事件
struct MouseEvent: InputEvent {
    let type: InputEventType
    let position: CGPoint
    let button: MouseButton
    
    init(type: InputEventType, position: CGPoint, button: MouseButton = .none) {
        self.type = type
        self.position = position
        self.button = button
    }
}

/// 键盘事件
struct KeyEvent: InputEvent {
    let type: InputEventType
    let keyCode: UInt16
    let keyChar: Character?
    let modifiers: UInt // 修饰键状态 (Shift, Control, Alt, Command等)

    init(type: InputEventType, keyCode: UInt16, keyChar: Character? = nil, modifiers: UInt = 0) {
        self.type = type
        self.keyCode = keyCode
        self.keyChar = keyChar
        self.modifiers = modifiers
    }
}

/// 滚动事件
struct ScrollEvent: InputEvent {
    let type: InputEventType = .scroll
    let deltaX: CGFloat
    let deltaY: CGFloat

    init(deltaX: CGFloat = 0, deltaY: CGFloat = 0) {
        self.deltaX = deltaX
        self.deltaY = deltaY
    }
}

/// 文本输入事件
struct InputTextEvent: InputEvent {
    let type: InputEventType = .input
    let text: String

    init(text: String) {
        self.text = text
    }
}

/// 触摸事件 (对应日志中的 DOWN/UP 事件)
struct TouchEvent: InputEvent {
    let type: InputEventType
    let position: CGPoint

    init(type: InputEventType, position: CGPoint) {
        self.type = type
        self.position = position
    }
}

/// 输入事件生成器，从WebRTC数据通道收到的消息创建输入事件
class InputEventFactory {
    /// 根据JSON数据创建输入事件
    /// - Parameter data: JSON数据
    /// - Returns: 输入事件
    static func createEvent(from data: Data) -> InputEvent? {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }
        
        guard let typeString = json["type"] as? String else {
            return nil
        }
        
        switch typeString {
        case "mouseMove":
            return createMouseMoveEvent(from: json)
        case "mouseDown":
            return createMouseDownEvent(from: json)
        case "mouseUp":
            return createMouseUpEvent(from: json)
        case "keyDown":
            return createKeyDownEvent(from: json)
        case "keyUp":
            return createKeyUpEvent(from: json)
        case "SCROLL":
            return createScrollEvent(from: json)
        case "INPUT":
            return createInputTextEvent(from: json)
        case "DOWN":
            return createTouchDownEvent(from: json)
        case "UP":
            return createTouchUpEvent(from: json)
        default:
            return nil
        }
    }
    
    private static func createMouseMoveEvent(from json: [String: Any]) -> MouseEvent? {
        guard let x = json["x"] as? CGFloat,
              let y = json["y"] as? CGFloat else {
            return nil
        }
        
        return MouseEvent(type: .mouseMove, position: CGPoint(x: x, y: y))
    }
    
    private static func createMouseDownEvent(from json: [String: Any]) -> MouseEvent? {
        guard let x = json["x"] as? CGFloat,
              let y = json["y"] as? CGFloat,
              let buttonRaw = json["button"] as? Int,
              let button = MouseButton(rawValue: buttonRaw) else {
            return nil
        }
        
        return MouseEvent(type: .mouseDown, position: CGPoint(x: x, y: y), button: button)
    }
    
    private static func createMouseUpEvent(from json: [String: Any]) -> MouseEvent? {
        guard let x = json["x"] as? CGFloat,
              let y = json["y"] as? CGFloat,
              let buttonRaw = json["button"] as? Int,
              let button = MouseButton(rawValue: buttonRaw) else {
            return nil
        }
        
        return MouseEvent(type: .mouseUp, position: CGPoint(x: x, y: y), button: button)
    }
    
    private static func createKeyDownEvent(from json: [String: Any]) -> KeyEvent? {
        guard let keyCode = json["keyCode"] as? UInt16 else {
            return nil
        }
        
        let keyChar = (json["keyChar"] as? String)?.first
        let modifiers = json["modifiers"] as? UInt ?? 0
        
        return KeyEvent(type: .keyDown, keyCode: keyCode, keyChar: keyChar, modifiers: modifiers)
    }
    
    private static func createKeyUpEvent(from json: [String: Any]) -> KeyEvent? {
        guard let keyCode = json["keyCode"] as? UInt16 else {
            return nil
        }

        let keyChar = (json["keyChar"] as? String)?.first
        let modifiers = json["modifiers"] as? UInt ?? 0

        return KeyEvent(type: .keyUp, keyCode: keyCode, keyChar: keyChar, modifiers: modifiers)
    }

    private static func createScrollEvent(from json: [String: Any]) -> ScrollEvent? {
        // 从日志看，滚动事件只有 h 参数，表示垂直滚动
        let deltaX: CGFloat = (json["w"] as? CGFloat) ?? 0
        let deltaY: CGFloat = (json["h"] as? CGFloat) ?? 0

        return ScrollEvent(deltaX: deltaX, deltaY: deltaY)
    }

    private static func createInputTextEvent(from json: [String: Any]) -> InputTextEvent? {
        guard let word = json["word"] as? String else {
            return nil
        }

        return InputTextEvent(text: word)
    }

    private static func createTouchDownEvent(from json: [String: Any]) -> TouchEvent? {
        guard let x = json["x"] as? CGFloat,
              let y = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .down, position: CGPoint(x: x, y: y))
    }

    private static func createTouchUpEvent(from json: [String: Any]) -> TouchEvent? {
        guard let x = json["x"] as? CGFloat,
              let y = json["y"] as? CGFloat else {
            return nil
        }

        return TouchEvent(type: .up, position: CGPoint(x: x, y: y))
    }
} 