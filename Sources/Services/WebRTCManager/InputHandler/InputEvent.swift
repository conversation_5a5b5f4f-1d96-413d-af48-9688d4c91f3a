import Foundation
import CoreGraphics
import UIKit

/// 输入事件类型
enum InputEventType: String {
    case input = "INPUT"   // 键盘输入
    case scroll = "SCROLL" // 滚动
    case down = "DOWN"     // 触摸按下
    case up = "UP"         // 触摸抬起
    case move = "MOVE"     // 触摸移动
}

/// 输入事件基类
protocol InputEvent {
    var type: InputEventType { get }
}

/// 键盘输入事件
struct KeyboardInputEvent: InputEvent {
    let type: InputEventType = .input
    let text: String
}

/// 滚动事件
struct ScrollEvent: InputEvent {
    let type: InputEventType = .scroll
    let deltaH: CGFloat
}

/// 触摸事件
struct TouchEvent: InputEvent {
    let type: InputEventType
    let positionX: CGFloat
    let positionY: CGFloat
}

/// 输入事件工厂
class InputEventFactory {
    static func createEvent(from data: Data) -> InputEvent? {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let typeString = json["type"] as? String,
              let eventType = InputEventType(rawValue: typeString) else {
            return nil
        }

        switch eventType {
        case .input:
            guard let word = json["word"] as? String else { return nil }
            return KeyboardInputEvent(text: word)

        case .scroll:
            guard let deltaH = json["h"] as? CGFloat else { return nil }
            return ScrollEvent(deltaH: deltaH)

        case .down, .up, .move:
            guard let posX = json["x"] as? CGFloat,
                  let posY = json["y"] as? CGFloat else { return nil }
            return TouchEvent(type: eventType, positionX: posX, positionY: posY)
        }
    }
}

// MARK: - IOKit 系统级输入处理

/// IOKit 类型定义
typealias IOHIDEventRef = UnsafeMutableRawPointer
typealias IOHIDEventSystemClientRef = UnsafeMutableRawPointer
typealias IOHIDDigitizerTransducerType = UInt32
typealias IOHIDFloat = Double
typealias IOOptionBits = UInt32
typealias IOHIDEventField = UInt32
typealias IOHIDEventOptionBits = UInt32

/// IOKit 常量定义
let kIOHIDDigitizerTransducerTypeHand: IOHIDDigitizerTransducerType = 0
let kIOHIDDigitizerEventRange: UInt32 = 1 << 0
let kIOHIDDigitizerEventTouch: UInt32 = 1 << 1
let kIOHIDDigitizerEventIdentity: UInt32 = 1 << 2
let kIOHIDDigitizerEventPosition: UInt32 = 1 << 3
let kIOHIDEventFieldIsBuiltIn: IOHIDEventField = 0x00010000
let kIOHIDEventFieldDigitizerIsDisplayIntegrated: IOHIDEventField = 0x00030001

/// IOKit 函数声明
@_silgen_name("IOHIDEventSystemClientCreate")
func IOHIDEventSystemClientCreate(_ allocator: CFAllocator?) -> IOHIDEventSystemClientRef?

@_silgen_name("IOHIDEventSystemClientDispatchEvent")
func IOHIDEventSystemClientDispatchEvent(_ client: IOHIDEventSystemClientRef, _ event: IOHIDEventRef)

@_silgen_name("IOHIDEventCreateDigitizerEvent")
func IOHIDEventCreateDigitizerEvent(_ allocator: CFAllocator?, _ timeStamp: UInt64, _ type: IOHIDDigitizerTransducerType, _ index: UInt32, _ identity: UInt32, _ eventMask: UInt32, _ buttonMask: UInt32, _ x: IOHIDFloat, _ y: IOHIDFloat, _ z: IOHIDFloat, _ tipPressure: IOHIDFloat, _ barrelPressure: IOHIDFloat, _ range: Bool, _ touch: Bool, _ options: IOOptionBits) -> IOHIDEventRef?

@_silgen_name("IOHIDEventCreateDigitizerFingerEvent")
func IOHIDEventCreateDigitizerFingerEvent(_ allocator: CFAllocator?, _ timeStamp: UInt64, _ index: UInt32, _ identity: UInt32, _ eventMask: UInt32, _ x: IOHIDFloat, _ y: IOHIDFloat, _ z: IOHIDFloat, _ tipPressure: IOHIDFloat, _ twist: IOHIDFloat, _ range: Bool, _ touch: Bool, _ options: IOOptionBits) -> IOHIDEventRef?

@_silgen_name("IOHIDEventCreateKeyboardEvent")
func IOHIDEventCreateKeyboardEvent(_ allocator: CFAllocator?, _ time: UInt64, _ page: UInt16, _ usage: UInt16, _ down: Bool, _ flags: IOHIDEventOptionBits) -> IOHIDEventRef?

@_silgen_name("IOHIDEventAppendEvent")
func IOHIDEventAppendEvent(_ parent: IOHIDEventRef, _ child: IOHIDEventRef)

@_silgen_name("IOHIDEventSetIntegerValue")
func IOHIDEventSetIntegerValue(_ event: IOHIDEventRef, _ field: IOHIDEventField, _ value: Int32)

@_silgen_name("IOHIDEventSetSenderID")
func IOHIDEventSetSenderID(_ event: IOHIDEventRef, _ sender: UInt64)

@_silgen_name("mach_absolute_time")
func mach_absolute_time() -> UInt64

/// 系统级输入事件处理器（单例）
class SystemInputHandler {
    static let shared = SystemInputHandler()

    private let logger = DaLog(subsystem: "com.iantsrtc.input", category: "SystemInputHandler")
    private var hidClient: IOHIDEventSystemClientRef?
    private let screenSize: CGSize

    // 触摸状态跟踪
    private var isCurrentlyTouching = false
    private var lastTouchX: CGFloat = 0
    private var lastTouchY: CGFloat = 0

    private init() {
        // 获取屏幕尺寸
        let screenBounds = UIScreen.main.bounds
        let screenScale = UIScreen.main.scale
        self.screenSize = CGSize(
            width: screenBounds.width * screenScale,
            height: screenBounds.height * screenScale
        )

        // 初始化 HID 客户端
        self.hidClient = IOHIDEventSystemClientCreate(kCFAllocatorDefault)

        logger.info("🎮 系统输入处理器初始化完成")
        logger.info("   屏幕尺寸: \(Int(screenSize.width))x\(Int(screenSize.height))")
    }

    /// 处理输入事件（主要入口方法）
    func handleInputEvent(jsonData: Data) {
        guard let inputEvent = InputEventFactory.createEvent(from: jsonData) else {
            logger.warning("⚠️ 无法解析输入事件")
            return
        }

        switch inputEvent.type {
        case .input:
            if let keyboardEvent = inputEvent as? KeyboardInputEvent {
                handleKeyboardInput(keyboardEvent)
            }
        case .scroll:
            if let scrollEvent = inputEvent as? ScrollEvent {
                handleScrollInput(scrollEvent)
            }
        case .down, .up, .move:
            if let touchEvent = inputEvent as? TouchEvent {
                handleTouchInput(touchEvent)
            }
        }
    }

    /// 处理键盘输入
    private func handleKeyboardInput(_ event: KeyboardInputEvent) {
        logger.debug("📝 键盘输入: \(event.text)")

        // TODO: 实现键盘输入的字符到按键映射
        // 这里需要将文本转换为具体的按键事件
        // 参考原始代码中的 VNCKeyboard 函数实现
    }

    /// 处理滚动事件
    private func handleScrollInput(_ event: ScrollEvent) {
        logger.debug("📜 滚动: \(event.deltaH)")

        // TODO: 实现滚动事件
        // 可以通过发送特殊的键盘事件来模拟滚动
    }

    /// 处理触摸事件
    private func handleTouchInput(_ event: TouchEvent) {
        // 转换相对坐标为绝对坐标
        let absoluteX = event.positionX * screenSize.width
        let absoluteY = event.positionY * screenSize.height

        // 归一化坐标 (0.0-1.0)
        let normalizedX = event.positionX
        let normalizedY = event.positionY

        logger.debug("👆 触摸\(event.type.rawValue): (\(Int(absoluteX)), \(Int(absoluteY)))")

        switch event.type {
        case .down:
            sendTouchEvent(normalizedX: normalizedX, normalizedY: normalizedY, isTouch: true, isFirstTouch: true)
            isCurrentlyTouching = true

        case .up:
            sendTouchEvent(normalizedX: normalizedX, normalizedY: normalizedY, isTouch: false, isFirstTouch: false)
            isCurrentlyTouching = false

        case .move:
            if isCurrentlyTouching {
                sendTouchEvent(normalizedX: normalizedX, normalizedY: normalizedY, isTouch: true, isFirstTouch: false)
            }

        default:
            break
        }

        lastTouchX = normalizedX
        lastTouchY = normalizedY
    }

    /// 发送触摸事件到系统
    private func sendTouchEvent(normalizedX: CGFloat, normalizedY: CGFloat, isTouch: Bool, isFirstTouch: Bool) {
        guard let client = hidClient else {
            logger.error("❌ HID客户端未初始化")
            return
        }

        let timestamp = mach_absolute_time()

        // 确定事件掩码
        var handEventMask: UInt32 = 0
        var fingerEventMask: UInt32 = 0

        if isFirstTouch && isTouch {
            // 开始触摸
            handEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch | kIOHIDDigitizerEventIdentity
            fingerEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch
        } else if isTouch && !isFirstTouch {
            // 移动触摸
            handEventMask = kIOHIDDigitizerEventPosition
            fingerEventMask = kIOHIDDigitizerEventPosition
        } else if !isTouch {
            // 结束触摸
            handEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch | kIOHIDDigitizerEventIdentity | kIOHIDDigitizerEventPosition
            fingerEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch
        } else {
            return
        }

        // 创建手部事件
        guard let handEvent = IOHIDEventCreateDigitizerEvent(
            kCFAllocatorDefault,
            timestamp,
            kIOHIDDigitizerTransducerTypeHand,
            1 << 22,
            1,
            handEventMask,
            0,
            normalizedX,
            normalizedY,
            0,
            0,
            0,
            false,
            false,
            0
        ) else {
            logger.error("❌ 创建手部事件失败")
            return
        }

        // 设置事件属性
        IOHIDEventSetIntegerValue(handEvent, kIOHIDEventFieldIsBuiltIn, 1)
        IOHIDEventSetIntegerValue(handEvent, kIOHIDEventFieldDigitizerIsDisplayIntegrated, 1)

        // 创建手指事件
        guard let fingerEvent = IOHIDEventCreateDigitizerFingerEvent(
            kCFAllocatorDefault,
            timestamp,
            3,
            2,
            fingerEventMask,
            normalizedX,
            normalizedY,
            0,
            0,
            0,
            isTouch,
            isTouch,
            0
        ) else {
            logger.error("❌ 创建手指事件失败")
            return
        }

        // 将手指事件附加到手部事件
        IOHIDEventAppendEvent(handEvent, fingerEvent)

        // 发送事件
        sendHIDEvent(handEvent)
    }

    /// 发送 HID 事件到系统
    private func sendHIDEvent(_ event: IOHIDEventRef) {
        guard let client = hidClient else {
            logger.error("❌ HID客户端未初始化")
            return
        }

        // 设置发送者ID（固定值，参考原始代码）
        IOHIDEventSetSenderID(event, 0x8000000817319372)

        // 分发事件
        IOHIDEventSystemClientDispatchEvent(client, event)

        // 释放事件
        // CFRelease(event) // Swift 会自动管理内存
    }
}