// RtcType.swift
import Foundation

/// WebRTC会话类型
enum RtcType: String, Codable {
    case iAnts      // 视频数据按正常流程走视频通道传输
    case dataAnt    // 视频数据需要编码后走数据通道传输
}

/// 统一的信令消息格式
struct SignalingMessage: Codable {
    let type: String
    let targetID: String?
    let sourceID: String?
    let data: SignalingData?
    let message: String?
    let success: Bool?
    let error: String?
    
    enum CodingKeys: String, CodingKey {
        case type, targetID, sourceID, data, message, success, error
    }
}

/// 信令数据内容
enum SignalingData: Codable {
    case string(String)
    case webrtcOffer(WebRTCOfferData)
    case webrtcAnswer(WebRTCAnswerData)
    case iceCandidate(IceCandidateData)
    case screenSettings([String: Any])
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let stringValue = try? container.decode(String.self) {
            self = .string(stringValue)
        } else if let offerData = try? container.decode(WebRTCOfferData.self) {
            self = .webrtcOffer(offerData)
        } else if let answerData = try? container.decode(WebRTCAnswerData.self) {
            self = .webrtcAnswer(answerData)
        } else if let candidateData = try? container.decode(IceCandidateData.self) {
            self = .iceCandidate(candidateData)
        } else {
            // 默认为空字典
            self = .screenSettings([:])
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch self {
        case .string(let value):
            try container.encode(value)
        case .webrtcOffer(let data):
            try container.encode(data)
        case .webrtcAnswer(let data):
            try container.encode(data)
        case .iceCandidate(let data):
            try container.encode(data)
        case .screenSettings:
            try container.encode("screenSettings")
        }
    }
}

/// WebRTC Offer数据
struct WebRTCOfferData: Codable {
    let type: String
    let sdp: String
}

/// WebRTC Answer数据
struct WebRTCAnswerData: Codable {
    let type: String
    let sdp: String
}

/// ICE候选数据
struct IceCandidateData: Codable {
    let sdpMLineIndex: Int
    let candidate: String
    let sdpMid: String
}

/// 信令消息类型常量
struct SignalingMessageType {
    static let socketOpen = "SOCKET_OPEN"
    static let socketMessage = "SOCKET_MESSAGE"
    static let socketClose = "SOCKET_CLOSE"
    static let register = "register"
    static let greeting = "greeting"
    static let offer = "offer"
    static let answer = "answer"
    static let iceCandidate = "ice-candidate"
    static let updateScreenSettings = "update_screen_settings"
    static let error = "error"
    static let execResult = "exec_result"
    static let `default` = "DEFAULT"
}
