import Foundation
import VideoToolbox
import CoreMedia
import IOSurface

class VideoCapturer: ScreenCaptureDelegate {
    let clientId: String
    let rtcType: RtcType

    // 两种模式的委托
    weak var pixelBufferDelegate: VideoCapturerPixelBufferDelegate?  // iAnts 模式
    weak var encodedDataDelegate: VideoCapturerEncodedDataDelegate?  // dataAnt 模式

    private let processingQueue = DispatchQueue(label: "com.iantsrtc.video.processing", qos: .userInitiated)
    private let encodingQueue = DispatchQueue(label: "com.iantsrtc.video.encoding", qos: .userInitiated)
    private let logger = DaLog(subsystem: "com.iantsrtc.video", category: "VideoCapturer")

    private var isActive = false
    private var frameCount = 0

    // VideoToolbox H264 编码器 (dataAnt 模式)
    private var compressionSession: VTCompressionSession?
    private var frameTimestamp: Int64 = 0

    init(clientId: String, type: RtcType) {
        self.clientId = clientId
        self.rtcType = type
        logger.info("🎥 VideoCapturer[\(clientId)-\(type.rawValue)] 创建")
    }
    
    /// 启动视频捕获
    func start() async {
        isActive = true
        frameCount = 0
        frameTimestamp = 0

        // 如果是 dataAnt 模式，初始化 H264 编码器
        if rtcType == .dataAnt {
            await setupH264Encoder()
        }

        logger.info("▶️ VideoCapturer[\(clientId)-\(rtcType.rawValue)] 启动")
    }

    /// 停止视频捕获
    func stop() async {
        isActive = false

        // 清理 H264 编码器
        if rtcType == .dataAnt {
            await cleanupH264Encoder()
        }

        logger.info("⏸️ VideoCapturer[\(clientId)-\(rtcType.rawValue)] 停止，处理了 \(frameCount) 帧")
    }
    
    // MARK: - ScreenCaptureDelegate

    func screenCaptureDidUpdateIOSurface(_ newSurface: IOSurfaceRef) {
        guard isActive else { return }

        frameCount += 1
        frameTimestamp += 33_333_333 // 约30fps，每帧间隔33.33ms

        // 根据模式选择不同的处理方式
        switch rtcType {
        case .iAnts:
            // iAnts 模式：转换为 CVPixelBuffer 通过视频轨道发送
            processingQueue.async { [weak self] in
                guard let self = self else { return }

                guard let pixelBuffer = self.convertIOSurfaceToPixelBuffer(newSurface) else {
                    return
                }

                let processedFrame = self.processVideoFrame(pixelBuffer)
                Task {
                    await self.pixelBufferDelegate?.videoCapturer(self, didCaptureVideoFrame: processedFrame)
                }
            }

        case .dataAnt:
            // dataAnt 模式：编码为 H264 通过数据通道发送
            encodingQueue.async { [weak self] in
                guard let self = self else { return }

                guard let pixelBuffer = self.convertIOSurfaceToPixelBuffer(newSurface) else {
                    return
                }

                self.encodeToH264(pixelBuffer: pixelBuffer, timestamp: self.frameTimestamp)
            }
        }
    }
    
    // MARK: - 视频处理

    private func convertIOSurfaceToPixelBuffer(_ surface: IOSurfaceRef) -> CVPixelBuffer? {
        var pixelBuffer: Unmanaged<CVPixelBuffer>?

        let width = IOSurfaceGetWidth(surface)
        let height = IOSurfaceGetHeight(surface)

        let attributes: [CFString: Any] = [
            kCVPixelBufferIOSurfacePropertiesKey: [:],
            kCVPixelBufferPixelFormatTypeKey: kCVPixelFormatType_420YpCbCr8BiPlanarFullRange,
            kCVPixelBufferWidthKey: width,
            kCVPixelBufferHeightKey: height
        ]

        let status = CVPixelBufferCreateWithIOSurface(
            kCFAllocatorDefault,
            surface,
            attributes as CFDictionary,
            &pixelBuffer
        )

        if status != kCVReturnSuccess {
            logger.error("❌ IOSurface转换失败: \(status)")
            return nil
        }

        return pixelBuffer?.takeRetainedValue()
    }

    private func processVideoFrame(_ pixelBuffer: CVPixelBuffer) -> CVPixelBuffer {
        // 这里可以添加视频处理逻辑：
        // - 缩放分辨率
        // - 调整编码参数
        // - 添加水印
        // - 格式转换等

        return pixelBuffer
    }

    // MARK: - H264 编码器 (dataAnt 模式)

    /// 设置 H264 编码器
    private func setupH264Encoder() async {
        // 获取屏幕尺寸 (这里使用默认值，实际应该从屏幕获取)
        let width = 1920
        let height = 1080

        var encoderSpecification: [CFString: Any] = [:]

        // 使用版本检查来避免 iOS 17.4+ 的 API
        if #available(iOS 17.4, *) {
            encoderSpecification[kVTVideoEncoderSpecification_EnableHardwareAcceleratedVideoEncoder] = true
            encoderSpecification[kVTVideoEncoderSpecification_RequireHardwareAcceleratedVideoEncoder] = false
        }

        let destinationImageBufferAttributes: [CFString: Any] = [
            kCVPixelBufferPixelFormatTypeKey: kCVPixelFormatType_420YpCbCr8BiPlanarFullRange,
            kCVPixelBufferWidthKey: width,
            kCVPixelBufferHeightKey: height
        ]

        let status = VTCompressionSessionCreate(
            allocator: kCFAllocatorDefault,
            width: Int32(width),
            height: Int32(height),
            codecType: kCMVideoCodecType_H264,
            encoderSpecification: encoderSpecification as CFDictionary,
            imageBufferAttributes: destinationImageBufferAttributes as CFDictionary,
            compressedDataAllocator: nil,
            outputCallback: { (outputCallbackRefCon, _, _, _, sampleBuffer) in
                guard let sampleBuffer = sampleBuffer else { return }

                let videoCapturer = Unmanaged<VideoCapturer>.fromOpaque(outputCallbackRefCon!).takeUnretainedValue()
                videoCapturer.handleEncodedFrame(sampleBuffer: sampleBuffer)
            },
            refcon: Unmanaged.passUnretained(self).toOpaque(),
            compressionSessionOut: &compressionSession
        )

        guard status == noErr, let session = compressionSession else {
            logger.error("❌ H264编码器创建失败: \(status)")
            return
        }

        // 配置编码器参数
        await configureH264Encoder(session: session)

        // 准备编码
        VTCompressionSessionPrepareToEncodeFrames(session)

        logger.info("✅ H264编码器[\(clientId)] 初始化成功")
    }

    /// 配置 H264 编码器参数
    private func configureH264Encoder(session: VTCompressionSession) async {
        // 设置实时编码
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_RealTime, value: kCFBooleanTrue)

        // 设置关键帧间隔 (每60帧一个关键帧，对应2秒@30fps)
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_MaxKeyFrameInterval, value: 60 as CFNumber)

        // 设置关键帧间隔时间 (每2秒一个关键帧)
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_MaxKeyFrameIntervalDuration, value: 2.0 as CFNumber)

        // 设置码率 (2Mbps)
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_AverageBitRate, value: 2_000_000 as CFNumber)

        // 设置数据率限制
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_DataRateLimits, value: [2_000_000, 1] as CFArray)

        // 设置帧率
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_ExpectedFrameRate, value: 30 as CFNumber)

        // 设置 H264 Profile (使用 Main Profile 而不是 Baseline)
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_ProfileLevel, value: kVTProfileLevel_H264_Main_AutoLevel)

        // 禁用帧重排序以减少延迟
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_AllowFrameReordering, value: kCFBooleanFalse)

        // 设置熵编码模式为 CABAC
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_H264EntropyMode, value: kVTH264EntropyMode_CABAC)

        // 强制生成 SPS/PPS 参数集
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_AllowTemporalCompression, value: kCFBooleanTrue)

        logger.debug("🔧 H264编码器参数配置完成")
    }

    /// 清理 H264 编码器
    private func cleanupH264Encoder() async {
        if let session = compressionSession {
            VTCompressionSessionCompleteFrames(session, untilPresentationTimeStamp: CMTime.invalid)
            VTCompressionSessionInvalidate(session)
            compressionSession = nil
            logger.info("🧹 H264编码器[\(clientId)] 已清理")
        }
    }

    /// 编码视频帧为 H264
    private func encodeToH264(pixelBuffer: CVPixelBuffer, timestamp: Int64) {
        guard let session = compressionSession else {
            logger.error("❌ H264编码器未初始化")
            return
        }

        let presentationTimeStamp = CMTime(value: timestamp, timescale: 1_000_000_000)

        let status = VTCompressionSessionEncodeFrame(
            session,
            imageBuffer: pixelBuffer,
            presentationTimeStamp: presentationTimeStamp,
            duration: CMTime.invalid,
            frameProperties: nil,
            sourceFrameRefcon: nil,
            infoFlagsOut: nil
        )

        if status != noErr {
            logger.error("❌ H264编码失败: \(status)")
        }
    }

    /// 处理编码后的 H264 帧
    private func handleEncodedFrame(sampleBuffer: CMSampleBuffer) {
        guard let dataBuffer = CMSampleBufferGetDataBuffer(sampleBuffer) else {
            logger.error("❌ 无法获取编码数据")
            return
        }

        var length: Int = 0
        var dataPointer: UnsafeMutablePointer<Int8>?

        let status = CMBlockBufferGetDataPointer(
            dataBuffer,
            atOffset: 0,
            lengthAtOffsetOut: nil,
            totalLengthOut: &length,
            dataPointerOut: &dataPointer
        )

        guard status == noErr, let pointer = dataPointer else {
            logger.error("❌ 无法获取编码数据指针")
            return
        }

        let rawData = Data(bytes: pointer, count: length)

        // 按照 Android 格式添加长度头：4字节长度 + H264数据
        var formattedData = Data()

        // 添加数据长度（大端序，4字节）
        var dataLength = UInt32(rawData.count).bigEndian
        formattedData.append(Data(bytes: &dataLength, count: 4))

        // 添加 H264 数据
        formattedData.append(rawData)

        // 检查是否是关键帧（SPS/PPS/IDR）
        let isKeyFrame = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: false) != nil
        if isKeyFrame {
            logger.debug("🔑 发送关键帧数据: \(formattedData.count) 字节")
        } else {
            logger.debug("📹 发送普通帧数据: \(formattedData.count) 字节")
        }

        // 通过委托发送编码数据
        Task {
            await encodedDataDelegate?.videoCapturer(self, didEncodeVideoData: formattedData)
        }
    }

    deinit {
        logger.info("♻️ VideoCapturer[\(clientId)-\(rtcType.rawValue)] 析构")
        // 同步清理编码器
        if let session = compressionSession {
            VTCompressionSessionCompleteFrames(session, untilPresentationTimeStamp: CMTime.invalid)
            VTCompressionSessionInvalidate(session)
        }
    }
}

// MARK: - 协议定义

/// iAnts 模式委托：处理 CVPixelBuffer 视频帧
protocol VideoCapturerPixelBufferDelegate: AnyObject {
    func videoCapturer(_ capturer: VideoCapturer, didCaptureVideoFrame frame: CVPixelBuffer) async
}

/// dataAnt 模式委托：处理编码后的 H264 数据
protocol VideoCapturerEncodedDataDelegate: AnyObject {
    func videoCapturer(_ capturer: VideoCapturer, didEncodeVideoData data: Data) async
}
