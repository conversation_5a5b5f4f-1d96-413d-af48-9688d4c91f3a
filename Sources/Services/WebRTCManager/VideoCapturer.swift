import Foundation
import VideoToolbox
import CoreMedia
import IOSurface
import UIKit

class VideoCapturer: ScreenCaptureDelegate {
    let clientId: String
    let rtcType: RtcType

    // 两种模式的委托
    weak var pixelBufferDelegate: VideoCapturerPixelBufferDelegate?  // iAnts 模式
    weak var encodedDataDelegate: VideoCapturerEncodedDataDelegate?  // dataAnt 模式

    private let processingQueue = DispatchQueue(label: "com.iantsrtc.video.processing", qos: .userInitiated)
    private let encodingQueue = DispatchQueue(label: "com.iantsrtc.video.encoding", qos: .userInitiated)
    private let logger = DaLog(subsystem: "com.iantsrtc.video", category: "VideoCapturer")

    private var isActive = false
    private var frameCount = 0

    // 屏幕尺寸
    private let screenWidth: Int
    private let screenHeight: Int

    // VideoToolbox H264 编码器 (dataAnt 模式)
    private var compressionSession: VTCompressionSession?
    private var frameTimestamp: Int64 = 0
    private var hasSentParameterSets = false

    init(clientId: String, type: RtcType) {
        self.clientId = clientId
        self.rtcType = type

        // 获取屏幕尺寸并按960高度等比例缩放
        let screenBounds = UIScreen.main.bounds
        let screenScale = UIScreen.main.scale
        let actualWidth = Int(screenBounds.width * screenScale)
        let actualHeight = Int(screenBounds.height * screenScale)

        // 设置目标高度为960，等比例计算宽度
        let targetHeight = 960
        let aspectRatio = Double(actualWidth) / Double(actualHeight)
        let targetWidth = Int(Double(targetHeight) * aspectRatio)

        self.screenWidth = targetWidth
        self.screenHeight = targetHeight

        logger.info("🎥 VideoCapturer[\(clientId)-\(type.rawValue)] 创建，屏幕尺寸: \(screenWidth)x\(screenHeight)")

        // 如果是 dataAnt 模式，直接初始化 H264 编码器
        if rtcType == .dataAnt {
            Task {
                await setupH264Encoder()
            }
        }
    }

    /// 启动视频捕获 (简化版，主要是设置状态)
    func start() async {
        isActive = true
        frameCount = 0
        frameTimestamp = 0

        // 如果是 dataAnt 模式，请求一个关键帧
        if rtcType == .dataAnt {
            requestKeyFrame()
        }

        logger.info("▶️ VideoCapturer[\(clientId)-\(rtcType.rawValue)] 启动")
    }

    /// 停止视频捕获
    func stop() async {
        isActive = false

        // 清理 H264 编码器
        if rtcType == .dataAnt {
            await cleanupH264Encoder()
        }

        logger.info("⏸️ VideoCapturer[\(clientId)-\(rtcType.rawValue)] 停止，处理了 \(frameCount) 帧")
    }
    
    // MARK: - ScreenCaptureDelegate

    func screenCaptureDidUpdateIOSurface(_ newSurface: IOSurfaceRef) {
        guard isActive else { return }

        frameCount += 1
        frameTimestamp += 33_333_333 // 约30fps，每帧间隔33.33ms

        // 根据模式选择不同的处理方式
        switch rtcType {
        case .iAnts:
            // iAnts 模式：转换为 CVPixelBuffer 通过视频轨道发送
            processingQueue.async { [weak self] in
                guard let self = self else { return }

                guard let pixelBuffer = self.convertIOSurfaceToPixelBuffer(newSurface) else {
                    return
                }

                let processedFrame = self.processVideoFrame(pixelBuffer)
                Task {
                    await self.pixelBufferDelegate?.videoCapturer(self, didCaptureVideoFrame: processedFrame)
                }
            }

        case .dataAnt:
            // dataAnt 模式：编码为 H264 通过数据通道发送
            encodingQueue.async { [weak self] in
                guard let self = self else { return }

                guard let pixelBuffer = self.convertIOSurfaceToPixelBuffer(newSurface) else {
                    return
                }

                self.encodeToH264(pixelBuffer: pixelBuffer, timestamp: self.frameTimestamp)
            }
        }
    }
    
    // MARK: - 视频处理

    private func convertIOSurfaceToPixelBuffer(_ surface: IOSurfaceRef) -> CVPixelBuffer? {
        var pixelBuffer: Unmanaged<CVPixelBuffer>?

        let width = IOSurfaceGetWidth(surface)
        let height = IOSurfaceGetHeight(surface)

        let attributes: [CFString: Any] = [
            kCVPixelBufferIOSurfacePropertiesKey: [:],
            kCVPixelBufferPixelFormatTypeKey: kCVPixelFormatType_420YpCbCr8BiPlanarFullRange,
            kCVPixelBufferWidthKey: width,
            kCVPixelBufferHeightKey: height
        ]

        let status = CVPixelBufferCreateWithIOSurface(
            kCFAllocatorDefault,
            surface,
            attributes as CFDictionary,
            &pixelBuffer
        )

        if status != kCVReturnSuccess {
            logger.error("❌ IOSurface转换失败: \(status)")
            return nil
        }

        return pixelBuffer?.takeRetainedValue()
    }

    private func processVideoFrame(_ pixelBuffer: CVPixelBuffer) -> CVPixelBuffer {
        // 这里可以添加视频处理逻辑：
        // - 缩放分辨率
        // - 调整编码参数
        // - 添加水印
        // - 格式转换等

        return pixelBuffer
    }

    // MARK: - H264 编码器 (dataAnt 模式)

    /// 设置 H264 编码器
    private func setupH264Encoder() async {
        // 使用初始化时获取的屏幕尺寸
        let width = self.screenWidth
        let height = self.screenHeight

        logger.info("🔧 设置H264编码器，使用屏幕尺寸: \(width)x\(height)")

        var encoderSpecification: [CFString: Any] = [:]

        // 使用版本检查来避免 iOS 17.4+ 的 API
        if #available(iOS 17.4, *) {
            encoderSpecification[kVTVideoEncoderSpecification_EnableHardwareAcceleratedVideoEncoder] = true
            encoderSpecification[kVTVideoEncoderSpecification_RequireHardwareAcceleratedVideoEncoder] = false
        }

        let destinationImageBufferAttributes: [CFString: Any] = [
            kCVPixelBufferPixelFormatTypeKey: kCVPixelFormatType_420YpCbCr8BiPlanarFullRange,
            kCVPixelBufferWidthKey: width,
            kCVPixelBufferHeightKey: height
        ]

        let status = VTCompressionSessionCreate(
            allocator: kCFAllocatorDefault,
            width: Int32(width),
            height: Int32(height),
            codecType: kCMVideoCodecType_H264,
            encoderSpecification: encoderSpecification as CFDictionary,
            imageBufferAttributes: destinationImageBufferAttributes as CFDictionary,
            compressedDataAllocator: nil,
            outputCallback: { (outputCallbackRefCon, _, _, _, sampleBuffer) in
                guard let sampleBuffer = sampleBuffer else { return }

                let videoCapturer = Unmanaged<VideoCapturer>.fromOpaque(outputCallbackRefCon!).takeUnretainedValue()
                videoCapturer.handleEncodedFrame(sampleBuffer: sampleBuffer)
            },
            refcon: Unmanaged.passUnretained(self).toOpaque(),
            compressionSessionOut: &compressionSession
        )

        guard status == noErr, let session = compressionSession else {
            logger.error("❌ H264编码器创建失败: \(status)")
            return
        }

        // 配置编码器参数
        await configureH264Encoder(session: session)

        // 准备编码
        VTCompressionSessionPrepareToEncodeFrames(session)

        logger.info("✅ H264编码器[\(clientId)] 初始化成功")
    }

    /// 配置 H264 编码器参数 (强制生成关键帧)
    private func configureH264Encoder(session: VTCompressionSession) async {
        // 设置实时编码
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_RealTime, value: kCFBooleanTrue)

        // 强制每30帧生成一个关键帧 (1秒@30fps)
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_MaxKeyFrameInterval, value: 30 as CFNumber)

        // 设置关键帧间隔时间 (每1秒一个关键帧)
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_MaxKeyFrameIntervalDuration, value: 1.0 as CFNumber)

        // 设置码率
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_AverageBitRate, value: 1_000_000 as CFNumber)

        // 设置帧率
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_ExpectedFrameRate, value: 30 as CFNumber)

        // 使用 Baseline Profile
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_ProfileLevel, value: kVTProfileLevel_H264_Baseline_AutoLevel)

        // 禁用帧重排序
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_AllowFrameReordering, value: kCFBooleanFalse)

        // 使用 CAVLC
        VTSessionSetProperty(session, key: kVTCompressionPropertyKey_H264EntropyMode, value: kVTH264EntropyMode_CAVLC)

        logger.debug("🔧 H264编码器参数配置完成 (强制关键帧)")
    }

    /// 清理 H264 编码器
    private func cleanupH264Encoder() async {
        if let session = compressionSession {
            VTCompressionSessionCompleteFrames(session, untilPresentationTimeStamp: CMTime.invalid)
            VTCompressionSessionInvalidate(session)
            compressionSession = nil
            logger.info("🧹 H264编码器[\(clientId)] 已清理")
        }
    }

    /// 请求关键帧
    private func requestKeyFrame() {
        guard compressionSession != nil else {
            logger.warning("⚠️ 编码器未初始化，无法请求关键帧")
            return
        }

        // 重置参数集发送状态，强制重新发送
        hasSentParameterSets = false

        logger.info("🔑 已请求关键帧")
    }

    /// 编码视频帧为 H264
    private func encodeToH264(pixelBuffer: CVPixelBuffer, timestamp: Int64) {
        guard let session = compressionSession else {
            logger.error("❌ H264编码器未初始化")
            return
        }

        let presentationTimeStamp = CMTime(value: timestamp, timescale: 1_000_000_000)

        // 每5分钟强制一个关键帧 (30fps * 5分钟 * 60秒 = 9000帧)
        var frameProperties: [CFString: Any]?
        let keyFrameInterval = 9000
        if frameCount % keyFrameInterval == 0 {
            frameProperties = [kVTEncodeFrameOptionKey_ForceKeyFrame: kCFBooleanTrue!]
            logger.info("🔑 强制生成关键帧 (帧号: \(frameCount), 间隔: \(keyFrameInterval)帧)")
        }

        let status = VTCompressionSessionEncodeFrame(
            session,
            imageBuffer: pixelBuffer,
            presentationTimeStamp: presentationTimeStamp,
            duration: CMTime.invalid,
            frameProperties: frameProperties as CFDictionary?,
            sourceFrameRefcon: nil,
            infoFlagsOut: nil
        )

        if status != noErr {
            logger.error("❌ H264编码失败: \(status)")
        }
    }

    /// 处理编码后的 H264 帧
    private func handleEncodedFrame(sampleBuffer: CMSampleBuffer) {
        guard let dataBuffer = CMSampleBufferGetDataBuffer(sampleBuffer) else {
            logger.error("❌ 无法获取编码数据")
            return
        }

        var length: Int = 0
        var dataPointer: UnsafeMutablePointer<Int8>?

        let status = CMBlockBufferGetDataPointer(
            dataBuffer,
            atOffset: 0,
            lengthAtOffsetOut: nil,
            totalLengthOut: &length,
            dataPointerOut: &dataPointer
        )

        guard status == noErr, let pointer = dataPointer else {
            logger.error("❌ 无法获取编码数据指针")
            return
        }

        let avccData = Data(bytes: pointer, count: length)

        // 将 AVCC 格式转换为 Annex B 格式
        let annexBData = convertAVCCToAnnexB(data: avccData)

        // 检查是否包含 IDR 帧（从实际数据中检测）
        let containsIDR = checkForIDRFrame(data: annexBData)
        let isKeyFrame = isKeyFrameFromSampleBuffer(sampleBuffer) || containsIDR

        // 只在第一次发送参数集（SPS/PPS），确保流开始时的正确顺序
        if !hasSentParameterSets {
            logger.info("🎬 首次发送，先发送 SPS/PPS 参数集")
            sendParameterSetsFromFormatDescription(sampleBuffer: sampleBuffer)
            hasSentParameterSets = true
        }

        // 只在关键帧时分析帧格式
        if isKeyFrame {
            analyzeH264Frame(data: annexBData, sampleBuffer: sampleBuffer, isKeyFrame: isKeyFrame)
        }

        // 直接发送纯净的 H264 数据，不添加长度头
        Task {
            await encodedDataDelegate?.videoCapturer(self, didEncodeVideoData: annexBData)
        }
    }

    /// 检查是否是关键帧
    private func isKeyFrameFromSampleBuffer(_ sampleBuffer: CMSampleBuffer) -> Bool {
        let attachments = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: false)

        if let attachmentsArray = attachments, CFArrayGetCount(attachmentsArray) > 0 {
            let attachment = CFArrayGetValueAtIndex(attachmentsArray, 0)
            let attachmentDict = Unmanaged<CFDictionary>.fromOpaque(attachment!).takeUnretainedValue()

            if let notSync = CFDictionaryGetValue(attachmentDict, Unmanaged.passUnretained(kCMSampleAttachmentKey_NotSync).toOpaque()) {
                return CFBooleanGetValue(Unmanaged<CFBoolean>.fromOpaque(notSync).takeUnretainedValue()) == false
            }
        }

        return false
    }

    /// 检查数据中是否包含 IDR 帧
    private func checkForIDRFrame(data: Data) -> Bool {
        var offset = 0

        while offset < data.count - 4 {
            // 查找起始码 0x00000001
            if data[offset] == 0x00 && data[offset + 1] == 0x00 &&
               data[offset + 2] == 0x00 && data[offset + 3] == 0x01 {

                let naluHeaderOffset = offset + 4
                if naluHeaderOffset < data.count {
                    let naluHeader = data[naluHeaderOffset]
                    let naluType = naluHeader & 0x1F

                    // 检查是否是 IDR 帧 (类型 5)
                    if naluType == 5 {
                        return true
                    }

                    offset = naluHeaderOffset + 1
                } else {
                    break
                }
            } else {
                offset += 1
            }
        }

        return false
    }

    /// 将 AVCC 格式转换为 Annex B 格式
    private func convertAVCCToAnnexB(data: Data) -> Data {
        var annexBData = Data()
        var offset = 0

        while offset < data.count {
            // 读取 NALU 长度（4字节，大端序）
            guard offset + 4 <= data.count else { break }

            let naluLength = data.subdata(in: offset..<offset+4).withUnsafeBytes { bytes in
                return UInt32(bigEndian: bytes.load(as: UInt32.self))
            }

            offset += 4

            // 检查 NALU 数据是否完整
            guard offset + Int(naluLength) <= data.count else { break }

            // 添加起始码
            annexBData.append(contentsOf: [0x00, 0x00, 0x00, 0x01])

            // 添加 NALU 数据
            annexBData.append(data.subdata(in: offset..<offset+Int(naluLength)))

            offset += Int(naluLength)
        }

        return annexBData
    }

    /// 从 FormatDescription 发送 SPS/PPS
    private func sendParameterSetsFromFormatDescription(sampleBuffer: CMSampleBuffer) {
        guard let formatDescription = CMSampleBufferGetFormatDescription(sampleBuffer) else {
            return
        }

        var parameterSetCount: Int = 0
        let status = CMVideoFormatDescriptionGetH264ParameterSetAtIndex(
            formatDescription, parameterSetIndex: 0, parameterSetPointerOut: nil,
            parameterSetSizeOut: nil, parameterSetCountOut: &parameterSetCount, nalUnitHeaderLengthOut: nil
        )

        if status == noErr && parameterSetCount > 0 {
            // 发送 SPS
            sendParameterSet(formatDescription: formatDescription, index: 0, type: "SPS")

            // 发送 PPS
            if parameterSetCount > 1 {
                sendParameterSet(formatDescription: formatDescription, index: 1, type: "PPS")
            }
        }
    }



    /// 发送单个参数集
    private func sendParameterSet(formatDescription: CMFormatDescription, index: Int, type: String) {
        var parameterSetPointer: UnsafePointer<UInt8>?
        var parameterSetSize: Int = 0

        let status = CMVideoFormatDescriptionGetH264ParameterSetAtIndex(
            formatDescription, parameterSetIndex: index, parameterSetPointerOut: &parameterSetPointer,
            parameterSetSizeOut: &parameterSetSize, parameterSetCountOut: nil, nalUnitHeaderLengthOut: nil
        )

        if status == noErr, let pointer = parameterSetPointer {
            // 创建带起始码的 NALU
            var naluData = Data()
            naluData.append(contentsOf: [0x00, 0x00, 0x00, 0x01]) // 起始码
            naluData.append(Data(bytes: pointer, count: parameterSetSize))

            logger.info("📤 发送\(type): \(parameterSetSize) 字节")

            // 直接发送纯净的 H264 数据，不添加长度头
            Task {
                await encodedDataDelegate?.videoCapturer(self, didEncodeVideoData: naluData)
            }
        }
    }

    /// 分析 H264 帧格式和内容 (简化版)
    private func analyzeH264Frame(data: Data, sampleBuffer: CMSampleBuffer, isKeyFrame: Bool) {
        // 分析 NALU 类型
        let naluInfo = analyzeNALUs(data: data)

        // 简化的关键帧信息打印
        if isKeyFrame {
            logger.info("🎬 关键帧: \(naluInfo), 大小: \(data.count) 字节")
        } else {
            logger.debug("📹 普通帧: \(naluInfo), 大小: \(data.count) 字节")
        }

        // 以下是原始分析代码，现在跳过
        return
        // 获取帧类型信息
        // let formatDescription = CMSampleBufferGetFormatDescription(sampleBuffer)
        // let mediaType = CMFormatDescriptionGetMediaType(formatDescription!)
        // let mediaSubType = CMFormatDescriptionGetMediaSubType(formatDescription!)

        // // 检查是否包含参数集
        // var spsData: Data?
        // var ppsData: Data?

        // if let formatDesc = formatDescription {
        //     var parameterSetCount: Int = 0
        //     let status = CMVideoFormatDescriptionGetH264ParameterSetAtIndex(
        //         formatDesc, parameterSetIndex: 0, parameterSetPointerOut: nil,
        //         parameterSetSizeOut: nil, parameterSetCountOut: &parameterSetCount, nalUnitHeaderLengthOut: nil
        //     )

        //     if status == noErr && parameterSetCount > 0 {
        //         // 提取 SPS
        //         var spsPointer: UnsafePointer<UInt8>?
        //         var spsSize: Int = 0
        //         let spsStatus = CMVideoFormatDescriptionGetH264ParameterSetAtIndex(
        //             formatDesc, parameterSetIndex: 0, parameterSetPointerOut: &spsPointer,
        //             parameterSetSizeOut: &spsSize, parameterSetCountOut: nil, nalUnitHeaderLengthOut: nil
        //         )

        //         if spsStatus == noErr, let sps = spsPointer {
        //             spsData = Data(bytes: sps, count: spsSize)
        //         }

        //         // 提取 PPS
        //         if parameterSetCount > 1 {
        //             var ppsPointer: UnsafePointer<UInt8>?
        //             var ppsSize: Int = 0
        //             let ppsStatus = CMVideoFormatDescriptionGetH264ParameterSetAtIndex(
        //                 formatDesc, parameterSetIndex: 1, parameterSetPointerOut: &ppsPointer,
        //                 parameterSetSizeOut: &ppsSize, parameterSetCountOut: nil, nalUnitHeaderLengthOut: nil
        //             )

        //             if ppsStatus == noErr, let pps = ppsPointer {
        //                 ppsData = Data(bytes: pps, count: ppsSize)
        //             }
        //         }
        //     }
        // }

        // // 这部分代码已经在上面处理过了，删除重复代码
        // var isDependedOn = false
        // let attachments = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: false)

        // if let attachmentsArray = attachments, CFArrayGetCount(attachmentsArray) > 0 {
        //     let attachment = CFArrayGetValueAtIndex(attachmentsArray, 0)
        //     let attachmentDict = Unmanaged<CFDictionary>.fromOpaque(attachment!).takeUnretainedValue()

        //     if let dependsOnOthers = CFDictionaryGetValue(attachmentDict, Unmanaged.passUnretained(kCMSampleAttachmentKey_DependsOnOthers).toOpaque()) {
        //         isDependedOn = CFBooleanGetValue(Unmanaged<CFBoolean>.fromOpaque(dependsOnOthers).takeUnretainedValue())
        //     }
        // }

        // // 打印详细信息
        // logger.info("🎬 H264帧分析:")
        // logger.info("   📊 媒体类型: \(String(describing: mediaType)), 子类型: \(String(describing: mediaSubType))")
        // logger.info("   � 原始数据大小: \(data.count) 字节")
        // logger.info("   �🔑 关键帧: \(isKeyFrame)")
        // logger.info("   🔗 依赖其他帧: \(isDependedOn)")
        // logger.info("   📦 NALU信息: \(naluInfo)")

        // if let sps = spsData {
        //     logger.info("   🧬 SPS大小: \(sps.count) 字节, 前8字节: \(sps.prefix(8).map { String(format: "%02X", $0) }.joined(separator: " "))")
        // }

        // if let pps = ppsData {
        //     logger.info("   🧬 PPS大小: \(pps.count) 字节, 前8字节: \(pps.prefix(8).map { String(format: "%02X", $0) }.joined(separator: " "))")
        // }

        // // 打印前32字节的十六进制内容
        // let hexPrefix = data.prefix(32).map { String(format: "%02X", $0) }.joined(separator: " ")
        // logger.info("   🔍 前32字节: \(hexPrefix)")
    }

    /// 分析 NALU 单元
    private func analyzeNALUs(data: Data) -> String {
        var naluTypes: [String] = []
        var offset = 0

        while offset < data.count - 4 {
            // 查找起始码 0x00000001 或 0x000001
            if data[offset] == 0x00 && data[offset + 1] == 0x00 {
                var startCodeLength = 0
                if data[offset + 2] == 0x00 && data[offset + 3] == 0x01 {
                    startCodeLength = 4
                } else if data[offset + 2] == 0x01 {
                    startCodeLength = 3
                }

                if startCodeLength > 0 {
                    let naluHeaderOffset = offset + startCodeLength
                    if naluHeaderOffset < data.count {
                        let naluHeader = data[naluHeaderOffset]
                        let naluType = naluHeader & 0x1F

                        let typeString = getNALUTypeString(type: naluType)
                        naluTypes.append("\(typeString)(\(naluType))")

                        offset = naluHeaderOffset + 1
                    } else {
                        break
                    }
                } else {
                    offset += 1
                }
            } else {
                offset += 1
            }
        }

        return naluTypes.joined(separator: ", ")
    }

    /// 获取 NALU 类型字符串
    private func getNALUTypeString(type: UInt8) -> String {
        switch type {
        case 1: return "P帧"
        case 5: return "IDR帧"
        case 7: return "SPS"
        case 8: return "PPS"
        case 6: return "SEI"
        case 9: return "AUD"
        default: return "其他"
        }
    }

    deinit {
        logger.info("♻️ VideoCapturer[\(clientId)-\(rtcType.rawValue)] 析构")
        // 同步清理编码器
        if let session = compressionSession {
            VTCompressionSessionCompleteFrames(session, untilPresentationTimeStamp: CMTime.invalid)
            VTCompressionSessionInvalidate(session)
        }
    }
}

// MARK: - 协议定义

/// iAnts 模式委托：处理 CVPixelBuffer 视频帧
protocol VideoCapturerPixelBufferDelegate: AnyObject {
    func videoCapturer(_ capturer: VideoCapturer, didCaptureVideoFrame frame: CVPixelBuffer) async
}

/// dataAnt 模式委托：处理编码后的 H264 数据
protocol VideoCapturerEncodedDataDelegate: AnyObject {
    func videoCapturer(_ capturer: VideoCapturer, didEncodeVideoData data: Data) async
}
