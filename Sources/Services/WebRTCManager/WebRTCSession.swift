// WebRTCSession.swift
import Foundation
import WebRTC
import IOSurface

/// WebRTC会话类，支持两种传输模式
class WebRTCSession: NSObject {
    let clientId: String
    let rtcType: RtcType
    let targetID: String

    private let rtcConfig: RTCConfig
    
    // MARK: - 委托
    weak var delegate: WebRTCSessionDelegate?
    
    // MARK: - 外部依赖
    private let signalingClient: SignalingClient
    private let videoCapturer: VideoCapturer?
    
    // MARK: - WebRTC核心组件
    private var peerConnection: RTCPeerConnection?
    private var localVideoTrack: RTCVideoTrack?
    private var localAudioTrack: RTCAudioTrack?
    private var videoSource: RTCVideoSource?
    private var dataChannel: RTCDataChannel?
    
    // MARK: - 工厂对象
    private let peerConnectionFactory: RTCPeerConnectionFactory
    private let rtcConfiguration: RTCConfiguration
    
    // MARK: - 状态管理
    private var connectionState: RTCIceConnectionState = .new
    private var isStarted = false
    private var isWebRTCInitialized = false
    private var hasReceivedAnswer = false  // 是否已收到 answer
    private var pendingIceCandidates: [RTCIceCandidate] = []  // 待发送的 ICE 候选

    private var screenCapture = ScreenCapture.shared
    
    // MARK: - 日志
    private let logger = DaLog(subsystem: "com.iantsrtc.webrtc", category: "WebRTCSession")
    
    // MARK: - 初始化
    
    init(clientId: String, rtcType: RtcType, targetID: String, signalingClient: SignalingClient) {
        self.clientId = clientId
        self.rtcType = rtcType
        self.targetID = targetID
        self.signalingClient = signalingClient

        // 配置STUN/TURN服务器
        self.rtcConfiguration = RTCConfiguration()

        // 使用默认的 RTCConfig
        self.rtcConfig = RTCConfig.default()

        // 设置ICE服务器
        var iceServers = [RTCIceServer]()

        // 添加STUN服务器
        if !rtcConfig.stunServers.isEmpty {
            iceServers.append(RTCIceServer(urlStrings: rtcConfig.stunServers))
            logger.debug("添加STUN服务器: \(rtcConfig.stunServers)")
        }

        // 添加TURN服务器
        for turnServer in rtcConfig.turnServers {
            iceServers.append(RTCIceServer(
                urlStrings: turnServer.urls,
                username: turnServer.username,
                credential: turnServer.credential
            ))
            logger.debug("添加TURN服务器: \(turnServer.urls)")
        }

        self.rtcConfiguration.iceServers = iceServers
        
        // 设置ICE传输策略
        self.rtcConfiguration.sdpSemantics = .unifiedPlan
        self.rtcConfiguration.disableLinkLocalNetworks = true
        self.rtcConfiguration.continualGatheringPolicy = .gatherContinually
        self.rtcConfiguration.iceTransportPolicy = .all
        self.rtcConfiguration.bundlePolicy = .maxBundle
        self.rtcConfiguration.rtcpMuxPolicy = .require

        // 创建媒体约束
        _ = RTCMediaConstraints(
            mandatoryConstraints: nil,
            optionalConstraints: ["DtlsSrtpKeyAgreement": kRTCMediaConstraintsValueTrue]  // true兼容浏览器
        )

        // 根据类型创建视频捕获器
        self.videoCapturer = VideoCapturer(clientId: clientId, type: rtcType)

        // 只有 iAnts 模式立即添加委托，dataAnt 模式等数据通道建立后再添加
        if rtcType == .iAnts {
            screenCapture.addDelegate(videoCapturer!)
        }

        // 创建PeerConnection工厂，暂时都默认
        // RTCInitializeSSL()
        let encoderFactory = RTCDefaultVideoEncoderFactory()
        let decoderFactory = RTCDefaultVideoDecoderFactory()
        // 使用自定义工厂创建 PeerConnectionFactory
        self.peerConnectionFactory = RTCPeerConnectionFactory(
            encoderFactory: encoderFactory,
            decoderFactory: decoderFactory
        )
        // self.peerConnectionFactory = RTCPeerConnectionFactory()

        super.init()

        // 设置委托
        switch rtcType {
        case .iAnts:
            videoCapturer?.pixelBufferDelegate = self
        case .dataAnt:
            videoCapturer?.encodedDataDelegate = self
        }

        logger.info("🔧 WebRTCSession[\(clientId)-\(rtcType.rawValue)] 初始化完成")
    }
    
    // MARK: - 会话管理
    
    /// 启动会话
    func start() throws {
        guard !isStarted else {
            logger.warning("⚠️ 会话[\(clientId)]已经启动")
            return
        }
        
        logger.info("🚀 启动WebRTC会话[\(clientId)-\(rtcType.rawValue)]")

        // 1. 创建WebRTC连接
        try createPeerConnection()

        // 2. 启动视频捕获器 // 分模式启动
        // 只有 iAnts 模式立即启动视频捕获，dataAnt 模式等数据通道建立后启动
        if rtcType == .iAnts {
            Task {
                await videoCapturer?.start()
            }
        }

        // 3. 创建Offer并发送
        try createOffer()
        
        isStarted = true
        logger.info("✅ WebRTC会话[\(clientId)-\(rtcType.rawValue)]启动成功")
    }
    
    /// 停止会话
    func stop() {
        guard isStarted else {
            logger.warning("⚠️ 会话[\(clientId)]未启动")
            return
        }
        
        logger.info("🛑 停止WebRTC会话[\(clientId)-\(rtcType.rawValue)]")
        
        // 1. 停止视频捕获器
        Task {
            await videoCapturer?.stop()
        }
        ScreenCapture.shared.removeDelegate(videoCapturer!)

        // 2. 关闭WebRTC连接
        closeWebRTCConnection()
        
        isStarted = false
        logger.info("✅ WebRTC会话[\(clientId)-\(rtcType.rawValue)]已停止")
    }
    
    // MARK: - WebRTC连接管理
    
    /// 创建PeerConnection
    private func createPeerConnection() throws {
        guard !isWebRTCInitialized else {
            logger.warning("⚠️ PeerConnection已创建")
            return
        }
        
        // 创建约束
        let constraints = RTCMediaConstraints(
            mandatoryConstraints: [kRTCMediaConstraintsOfferToReceiveAudio: kRTCMediaConstraintsValueFalse,
                                   kRTCMediaConstraintsOfferToReceiveVideo: kRTCMediaConstraintsValueFalse],
            optionalConstraints: ["DtlsSrtpKeyAgreement": kRTCMediaConstraintsValueTrue]
        )
        
        // 创建PeerConnection
        peerConnection = peerConnectionFactory.peerConnection(
            with: self.rtcConfiguration,
            constraints: constraints,
            delegate: self
        )
        
        guard peerConnection != nil else {
            throw WebRTCError.failedToCreatePeerConnection
        }
        
        // 根据类型创建媒体流
        if rtcType == .iAnts {
            try createLocalMediaStream()
        }
        try createDataChannel()
        
        isWebRTCInitialized = true
        logger.info("✅ PeerConnection[\(clientId)-\(rtcType.rawValue)] 创建成功")
    }
    
    /// 创建本地媒体流 (iAnts模式)
    private func createLocalMediaStream() throws {
        guard let peerConnection = peerConnection else {
            throw WebRTCError.peerConnectionNotInitialized
        }
        
        // 创建视频源
        videoSource = peerConnectionFactory.videoSource()
        
        // 创建视频轨道
        localVideoTrack = peerConnectionFactory.videoTrack(
            with: videoSource!,
            trackId: "video_\(clientId)"
        )
        
        // 创建音频轨道（可选）
        let audioSource = peerConnectionFactory.audioSource(with: nil)
        localAudioTrack = peerConnectionFactory.audioTrack(
            with: audioSource,
            trackId: "audio_\(clientId)"
        )
        
        // 添加轨道到PeerConnection
        if let videoTrack = localVideoTrack {
            peerConnection.add(videoTrack, streamIds: ["stream_\(clientId)"])
            logger.info("📹 视频轨道已添加 (iAnts模式)")
        }
        
        if let audioTrack = localAudioTrack {
            peerConnection.add(audioTrack, streamIds: ["stream_\(clientId)"])
            logger.info("🎵 音频轨道已添加 (iAnts模式)")
        }
    }
    
    /// 创建数据通道 (dataAnt 模式)
    private func createDataChannel() throws {
        guard let peerConnection = peerConnection else {
            throw WebRTCError.peerConnectionNotInitialized
        }
        
        // 配置数据通道
        let dataChannelConfig = RTCDataChannelConfiguration()
        dataChannelConfig.isOrdered = true      // 保证顺序
        dataChannelConfig.isNegotiated = false  // 自动协商
        dataChannelConfig.channelId = 1
        
        // 创建数据通道
        dataChannel = peerConnection.dataChannel(
            forLabel: "videoData_\(clientId)",
            configuration: dataChannelConfig
        )
        
        dataChannel?.delegate = self
        
        logger.info("📡 数据通道已创建 (dataAnt模式)")
    }
    
    /// 关闭WebRTC连接
    private func closeWebRTCConnection() {
        logger.info("🔌 关闭WebRTC连接[\(clientId)-\(rtcType.rawValue)]")
        
        // 停止轨道
        localVideoTrack?.isEnabled = false
        localAudioTrack?.isEnabled = false
        
        // 关闭数据通道
        dataChannel?.close()
        
        // 关闭PeerConnection
        peerConnection?.close()
        
        // 清理资源
        peerConnection = nil
        localVideoTrack = nil
        localAudioTrack = nil
        videoSource = nil
        dataChannel = nil
        
        isWebRTCInitialized = false
        connectionState = .closed
        
        logger.info("✅ WebRTC连接[\(clientId)-\(rtcType.rawValue)]已关闭")
    }
    
    // MARK: - 信令处理
    
    /// 创建Offer
    private func createOffer() throws {
        guard let peerConnection = peerConnection else {
            throw WebRTCError.peerConnectionNotInitialized
        }

        let constraints = RTCMediaConstraints(
            mandatoryConstraints: [kRTCMediaConstraintsOfferToReceiveAudio: kRTCMediaConstraintsValueFalse,
                                   kRTCMediaConstraintsOfferToReceiveVideo: kRTCMediaConstraintsValueFalse],
            optionalConstraints: ["DtlsSrtpKeyAgreement": kRTCMediaConstraintsValueTrue]
        )

        peerConnection.offer(for: constraints) { [weak self] description, error in
            guard let self = self else { return }

            if let error = error {
                self.logger.error("❌ 创建Offer失败: \(error)")
                return
            }

            guard let description = description else {
                self.logger.error("❌ Offer描述为空")
                return
            }

            // 设置本地描述
            peerConnection.setLocalDescription(description) { [weak self] error in
                guard let self = self else { return }

                if let error = error {
                    self.logger.error("❌ 设置本地描述失败: \(error)")
                } else {
                    self.logger.info("✅ Offer创建并设置成功")
                    // 发送Offer到信令服务器
                    Task {
                        await self.signalingClient.sendOffer(description.sdp, to: self.targetID)
                    }
                }
            }
        }
    }
    
    /// 处理远程Answer
    func handleAnswer(_ sdp: String) throws {
        guard let peerConnection = peerConnection else {
            throw WebRTCError.peerConnectionNotInitialized
        }

        // 检查当前信令状态
        let currentState = peerConnection.signalingState
        logger.debug("🔍 当前信令状态: \(currentState)")

        // 如果状态是 stable，说明可能是控制端重新连接，需要重置连接
        if currentState == .stable {
            logger.warning("⚠️ 检测到控制端可能重新连接，重置WebRTC连接")
            try resetConnection()
        }

        // 如果状态不是 haveLocalOffer，说明连接状态异常，需要重新创建 Offer
        if currentState != .haveLocalOffer {
            logger.warning("⚠️ 信令状态异常(\(currentState))，重新创建Offer")
            try createOffer()

            // 等待状态变为 haveLocalOffer 后再处理 Answer
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                self?.setRemoteAnswer(sdp)
            }
        } else {
            setRemoteAnswer(sdp)
        }
    }

    /// 设置远程Answer
    private func setRemoteAnswer(_ sdp: String) {
        guard let peerConnection = peerConnection else {
            logger.error("❌ PeerConnection未初始化")
            return
        }

        let sessionDescription = RTCSessionDescription(type: .answer, sdp: sdp)

        peerConnection.setRemoteDescription(sessionDescription) { [weak self] error in
            guard let self = self else { return }

            if let error = error {
                self.logger.error("❌ 设置远程Answer失败: \(error)")
            } else {
                self.logger.info("✅ 远程Answer已处理")

                // 标记已收到 answer
                self.hasReceivedAnswer = true

                // 发送所有待发送的 ICE 候选
                self.sendPendingIceCandidates()
            }
        }
    }

    /// 重置WebRTC连接
    private func resetConnection() throws {
        logger.info("🔄 重置WebRTC连接")

        // 重置状态
        hasReceivedAnswer = false
        pendingIceCandidates.removeAll()
        connectionState = .new

        // 关闭旧连接
        closeWebRTCConnection()

        // 重新创建连接
        try createPeerConnection()

        logger.info("✅ WebRTC连接重置完成")
    }

    /// 发送所有待发送的 ICE 候选
    private func sendPendingIceCandidates() {
        logger.info("📤 开始发送待发送的ICE候选，总数: \(pendingIceCandidates.count)")

        for candidate in pendingIceCandidates {
            Task {
                await signalingClient.sendIceCandidate(candidate, to: targetID)
            }
        }

        // 清空待发送列表
        pendingIceCandidates.removeAll()
        logger.info("✅ 所有待发送的ICE候选已发送")
    }
    
    /// 处理ICE候选
    func handleIceCandidate(_ candidateData: IceCandidateData) {
        guard let peerConnection = peerConnection else {
            logger.error("❌ PeerConnection未初始化，无法处理ICE候选")
            return
        }
        
        let candidate = RTCIceCandidate(
            sdp: candidateData.candidate,
            sdpMLineIndex: Int32(candidateData.sdpMLineIndex),
            sdpMid: candidateData.sdpMid
        )
        
        Task {
            do {
                try await peerConnection.add(candidate)
                logger.debug("✅ ICE候选已添加")
            } catch {
                logger.error("❌ 添加ICE候选失败: \(error)")
            }
        }
    }
    
    // MARK: - 视频处理
    
    /// 发送视频帧 (iAnts 模式)
    private func sendVideoFrame(_ pixelBuffer: CVPixelBuffer) {
        guard rtcType == .iAnts, let videoSource = videoSource else {
            return
        }
        
        // 创建RTCVideoFrame
        let timeStamp = Int64(CACurrentMediaTime() * 1000_000_000)
        let rtcPixelBuffer = RTCCVPixelBuffer(pixelBuffer: pixelBuffer)
        let videoFrame = RTCVideoFrame(buffer: rtcPixelBuffer, rotation: ._0, timeStampNs: timeStamp)
        
        // 发送帧到视频源
        videoSource.capturer(RTCVideoCapturer(), didCapture: videoFrame)
    }
    
    /// 发送编码视频数据 (dataAnt 模式)
    private func sendEncodedVideoData(_ data: Data) {
        guard rtcType == .dataAnt, let dataChannel = dataChannel else {
            logger.warning("⚠️ 数据通道不可用，无法发送视频数据")
            return
        }

        // 检查数据通道状态
        guard dataChannel.readyState == .open else {
            logger.warning("⚠️ 数据通道未打开，状态: \(dataChannel.readyState)")
            return
        }

        let buffer = RTCDataBuffer(data: data, isBinary: true)
        let success = dataChannel.sendData(buffer)

        if !success {
            logger.error("❌ 发送视频数据失败，数据大小: \(data.count) 字节")
        }
        //  else {
            // logger.debug("✅ 发送视频数据成功，大小: \(data.count) 字节")
        // }
    }
    
    // MARK: - 状态查询
    
    /// 获取连接状态
    func getConnectionState() -> RTCIceConnectionState {
        return connectionState
    }
    
    /// 检查连接是否断开
    func isDisconnected() -> Bool {
        return connectionState == .disconnected || connectionState == .failed || connectionState == .closed
    }
    
    deinit {
        logger.info("♻️ WebRTCSession[\(clientId)-\(rtcType.rawValue)] 析构")
        // 同步清理资源
        peerConnection?.close()
    }
}

// MARK: - VideoCapturerPixelBufferDelegate (iAnts 模式)

extension WebRTCSession: VideoCapturerPixelBufferDelegate {
    func videoCapturer(_ capturer: VideoCapturer, didCaptureVideoFrame frame: CVPixelBuffer) async {
        sendVideoFrame(frame)
    }
}

// MARK: - VideoCapturerEncodedDataDelegate (dataAnt 模式)

extension WebRTCSession: VideoCapturerEncodedDataDelegate {
    func videoCapturer(_ capturer: VideoCapturer, didEncodeVideoData data: Data) async {
        sendEncodedVideoData(data)
    }
}

// MARK: - RTCPeerConnectionDelegate

extension WebRTCSession: RTCPeerConnectionDelegate {
    func peerConnection(_ peerConnection: RTCPeerConnection, didChange stateChanged: RTCSignalingState) {
        logger.info("📡 信令状态变化: \(stateChanged)")
    }

    func peerConnection(_ peerConnection: RTCPeerConnection, didAdd stream: RTCMediaStream) {
        logger.info("📺 收到远程流")
    }

    func peerConnection(_ peerConnection: RTCPeerConnection, didRemove stream: RTCMediaStream) {
        logger.info("📺 移除远程流")
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didGenerate candidate: RTCIceCandidate) {
        logger.debug("🧊 生成ICE候选")

        if hasReceivedAnswer {
            // 已收到 answer，可以立即发送 ICE 候选
            logger.debug("✅ 已收到Answer，立即发送ICE候选")
            Task {
                await signalingClient.sendIceCandidate(candidate, to: targetID)
            }
        } else {
            // 还未收到 answer，暂存 ICE 候选
            pendingIceCandidates.append(candidate)
            logger.debug("⏳ 未收到Answer，暂存ICE候选 (总数: \(pendingIceCandidates.count))")
        }
    }

    func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceConnectionState) {
        connectionState = newState
        logger.info("🔗 ICE连接状态变化: \(newState)")

        // 详细记录各种连接状态
        switch newState {
        case .new:
            logger.debug("🆕 ICE连接状态: 新建")
        case .checking:
            logger.debug("🔍 ICE连接状态: 检查中")
        case .connected:
            logger.info("✅ ICE连接状态: 已连接")
            // 连接建立后，请求关键帧确保视频能正常显示
            requestKeyFrameFromCapturer()
        case .completed:
            logger.info("🎉 ICE连接状态: 完成")
            // 连接完成后，也请求一个关键帧
            requestKeyFrameFromCapturer()
        case .failed:
            logger.error("❌ ICE连接状态: 失败")
        case .disconnected:
            logger.warning("⚠️ ICE连接状态: 断开")
        case .closed:
            logger.info("🔒 ICE连接状态: 关闭")
        case .count:
            logger.debug("📊 ICE连接状态: 计数")
        @unknown default:
            logger.warning("❓ ICE连接状态: 未知(\(newState.rawValue))")
        }

        // 通知管理器连接状态变化
        if isDisconnected() {
            logger.warning("🚨 检测到连接断开，通知管理器")
            delegate?.webRTCSessionDidDisconnect(self)
        }
    }

    /// 从视频捕获器请求关键帧
    private func requestKeyFrameFromCapturer() {
        if rtcType == .dataAnt, let capturer = videoCapturer {
            capturer.forceNextKeyFrame()
            logger.debug("🔑 已向视频捕获器请求关键帧")
        }
    }

    func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceGatheringState) {
        logger.debug("❄️ ICE收集状态变化: \(newState)")
    }

    func peerConnection(_ peerConnection: RTCPeerConnection, didRemove candidates: [RTCIceCandidate]) {
        logger.debug("🗑️ 移除ICE候选")
    }

    func peerConnection(_ peerConnection: RTCPeerConnection, didOpen dataChannel: RTCDataChannel) {
        logger.info("📡 数据通道已打开")
    }

    func peerConnectionShouldNegotiate(_ peerConnection: RTCPeerConnection) {
        logger.debug("🔄 需要重新协商")
    }
}

// MARK: - RTCDataChannelDelegate (dataAnt 模式)

extension WebRTCSession: RTCDataChannelDelegate {
    func dataChannelDidChangeState(_ dataChannel: RTCDataChannel) {
        logger.info("📡 数据通道状态变化: \(dataChannel.readyState)")

        switch dataChannel.readyState {
        case .connecting:
            logger.info("🔄 数据通道连接中...")
        case .open:
            logger.info("✅ 数据通道已打开，可以发送数据")
            // dataAnt 模式：数据通道打开后才添加代理和启动
            if rtcType == .dataAnt, let videoCapturer = videoCapturer {
                logger.info("🎬 数据通道已打开，添加视频捕获代理")

                self.screenCapture.addDelegate(videoCapturer)
                Task {
                    await videoCapturer.start()
                }
            }
        case .closing:
            logger.warning("⚠️ 数据通道关闭中...")
        case .closed:
            logger.warning("❌ 数据通道已关闭")
        @unknown default:
            logger.warning("❓ 数据通道未知状态: \(dataChannel.readyState)")
        }
    }

    func dataChannel(_ dataChannel: RTCDataChannel, didReceiveMessageWith buffer: RTCDataBuffer) {
        logger.debug("📥 收到数据通道消息: \(buffer.data.count) 字节")

        // 调试：打印收到的所有数据
        let receivedData = buffer.data

        // 尝试解析为字符串
        if let stringData = String(data: receivedData, encoding: .utf8) {
            logger.debug("📥 收到数据内容(UTF8): \(stringData)")
        }

        // 尝试解析为JSON
        do {
            let jsonObject = try JSONSerialization.jsonObject(with: receivedData, options: [])
            logger.debug("📥 收到数据内容(JSON): \(jsonObject)")

            // 处理输入事件
            handleInputEvent(jsonData: receivedData)
        } catch {
            logger.debug("📥 数据不是有效的JSON格式: \(error)")
        }
    }

    /// 处理输入事件（使用系统级处理器）
    private func handleInputEvent(jsonData: Data) {
        // 使用系统级输入处理器处理输入事件
        SystemInputHandler.shared.handleInputEvent(jsonData: jsonData)
    }
}

// MARK: - 协议定义

protocol WebRTCSessionDelegate: AnyObject {
    func webRTCSessionDidDisconnect(_ session: WebRTCSession)
}

enum WebRTCError: Error {
    case failedToCreatePeerConnection
    case peerConnectionNotInitialized
    case failedToCreateOffer
    case failedToCreateAnswer
    case failedToSetLocalDescription
    case failedToSetRemoteDescription
    
    var localizedDescription: String {
        switch self {
        case .failedToCreatePeerConnection:
            return "创建PeerConnection失败"
        case .peerConnectionNotInitialized:
            return "PeerConnection未初始化"
        case .failedToCreateOffer:
            return "创建Offer失败"
        case .failedToCreateAnswer:
            return "创建Answer失败"
        case .failedToSetLocalDescription:
            return "设置本地描述失败"
        case .failedToSetRemoteDescription:
            return "设置远程描述失败"
        }
    }
}
