import os

let logG = DaLog(subsystem: "com.iantsrtc.capture", category: "Log")
/// Log 类：基于 os.Logger 的二次封装
/// 提供统一的日志记录接口，支持不同的日志级别
class DaLog {
    private let logger: Logger
    
    /// 初始化 Log 实例
    /// - Parameters:
    ///   - subsystem: 子系统标识符，通常使用反向域名格式，如 "com.yourcompany.yourapp"
    ///   - category: 日志分类，用于区分不同模块或功能
    init(subsystem: String, category: String) {
        self.logger = Logger(subsystem: subsystem, category: category)
    }
    
    /// 调试级别日志
    /// 用于详细的调试信息，仅在调试时显示
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        logger.debug("\(message, privacy: .public)")
    }
    
    /// 信息级别日志
    /// 用于一般的信息记录
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        logger.info("\(message, privacy: .public)")
    }
    
    /// 通知级别日志
    /// 用于重要的状态变化或事件
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func notice(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        logger.notice("\(message, privacy: .public)")
    }
    
    /// 警告级别日志
    /// 用于可能导致问题的情况
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        logger.warning("\(message, privacy: .public)")
    }
    
    /// 错误级别日志
    /// 用于错误情况的记录
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        logger.error("\(message, privacy: .public)")
    }
    
    /// 故障级别日志
    /// 用于严重的系统错误或故障
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func fault(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        logger.fault("\(message, privacy: .public)")
    }
}
